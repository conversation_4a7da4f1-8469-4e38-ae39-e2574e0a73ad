{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,8DAAgD;AAChD,sDAAwC;AACxC,mCAAgC;AAChC,gDAAwB;AACxB,sDAA8B;AAC9B,yCAA2B;AAE3B,4BAA4B;AAC5B,KAAK,CAAC,aAAa,EAAE,CAAC;AAEtB,kCAAkC;AAClC,IAAI,MAAc,CAAC;AAEnB,SAAS,SAAS;;IAChB,IAAI,CAAC,MAAM,EAAE;QACX,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,KAAI,MAAA,SAAS,CAAC,MAAM,EAAE,CAAC,MAAM,0CAAE,OAAO,CAAA,CAAC;QAChF,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAI,KAAK,CAAC,sEAAsE,CAAC,CAAC;SACzF;QACD,MAAM,GAAG,IAAI,eAAM,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC;KACjC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,mCAAmC;AACnC,MAAM,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;AACtB,GAAG,CAAC,GAAG,CAAC,IAAA,cAAI,EAAC,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;AAChC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,EAAE,CAAC,CAAC;AAExB,qBAAqB;AACrB,MAAM,qBAAqB,GAAG,GAAG,CAAC,MAAM,CAAC;IACvC,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC;IACpD,WAAW,EAAE,GAAG,CAAC,MAAM,CAAC;QACtB,mBAAmB,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QAChE,SAAS,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QACtD,kBAAkB,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;QAC/D,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QAClD,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;QACxD,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3C,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;QACzC,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;KACxC,CAAC,CAAC,QAAQ,EAAE;IACb,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO;CAC1D,CAAC,CAAC;AAEH,MAAM,uBAAuB,GAAG,GAAG,CAAC,MAAM,CAAC;IACzC,SAAS,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC5D,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC;CAC3C,CAAC,CAAC;AAEH,gCAAgC;AAChC,MAAM,gBAAgB,GAAG,KAAK,EAAE,GAAoB,EAAE,GAAqB,EAAE,IAA0B,EAAiB,EAAE;IACxH,IAAI;QACF,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;YACpD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uCAAuC,EAAE,CAAC,CAAC;YACzE,OAAO;SACR;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC7D,GAAG,CAAC,IAAI,CAAC,MAAM,GAAG,YAAY,CAAC,GAAG,CAAC;QACnC,IAAI,EAAE,CAAC;KACR;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,CAAC;QAC9C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,6BAA6B,EAAE,CAAC,CAAC;QAC/D,OAAO;KACR;AACH,CAAC,CAAC;AAEF,uDAAuD;AACvD,GAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE,KAAK,EAAE,GAAoB,EAAE,GAAqB,EAAiB,EAAE;;IACnG,IAAI;QACF,mBAAmB;QACnB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,qBAAqB,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QAClE,IAAI,KAAK,EAAE;YACT,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1D,OAAO;SACR;QAED,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;QAEhD,2BAA2B;QAC3B,MAAM,MAAM,GAAG,oBAAoB,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;QAE3D,kCAAkC;QAClC,MAAM,UAAU,GAAG,MAAM,SAAS,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YAC3D,KAAK,EAAE,QAAQ;YACf,QAAQ,EAAE;gBACR;oBACE,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,6IAA6I;iBACvJ;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM;iBAChB;aACF;YACD,WAAW,EAAE,GAAG;YAChB,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,MAAA,MAAA,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,0CAAE,OAAO,0CAAE,OAAO,CAAC;QAC7D,IAAI,CAAC,YAAY,EAAE;YACjB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;SACjD;QAED,oCAAoC;QACpC,MAAM,QAAQ,GAAG,aAAa,CAAC,YAAY,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;QAEpE,oBAAoB;QACpB,MAAM,KAAK,CAAC,SAAS,EAAE;aACpB,UAAU,CAAC,YAAY,CAAC;aACxB,GAAG,CAAC;YACH,MAAM;YACN,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;YACvD,MAAM,EAAE,QAAQ;SACjB,CAAC,CAAC;QAEL,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,QAAQ;YACR,OAAO,EAAE,kCAAkC;SAC5C,CAAC,CAAC;KAEJ;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,KAAK,EAAE,8BAA8B;YACrC,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SAClE,CAAC,CAAC;KACJ;AACH,CAAC,CAAC,CAAC;AAEH,iCAAiC;AACjC,GAAG,CAAC,IAAI,CAAC,oBAAoB,EAAE,gBAAgB,EAAE,KAAK,EAAE,GAAoB,EAAE,GAAqB,EAAiB,EAAE;;IACpH,IAAI;QACF,mBAAmB;QACnB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,uBAAuB,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACpE,IAAI,KAAK,EAAE;YACT,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC;YAC1D,OAAO;SACR;QAED,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,KAAK,CAAC;QAErC,uCAAuC;QACvC,MAAM,MAAM,GAAG,6BAA6B,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAEjE,iCAAiC;QACjC,MAAM,UAAU,GAAG,MAAM,SAAS,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YAC3D,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE;gBACR;oBACE,IAAI,EAAE,QAAQ;oBACd,OAAO,EAAE,kGAAkG;iBAC5G;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,MAAM;iBAChB;aACF;YACD,WAAW,EAAE,GAAG;YAChB,UAAU,EAAE,IAAI;SACjB,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,MAAA,MAAA,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,0CAAE,OAAO,0CAAE,OAAO,CAAC;QAC9D,IAAI,CAAC,aAAa,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;SAChD;QAED,uBAAuB;QACvB,MAAM,aAAa,GAAG,kBAAkB,CAAC,aAAa,CAAC,CAAC;QAExD,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,SAAS,EAAE,aAAa;YACxB,OAAO,EAAE,8BAA8B;SACxC,CAAC,CAAC;KAEJ;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACnD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,KAAK,EAAE,6BAA6B;YACpC,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SAClE,CAAC,CAAC;KACJ;AACH,CAAC,CAAC,CAAC;AAEH,wBAAwB;AACxB,GAAG,CAAC,GAAG,CAAC,qBAAqB,EAAE,gBAAgB,EAAE,KAAK,EAAE,GAAoB,EAAE,GAAqB,EAAE,EAAE;IACrG,IAAI;QACF,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;QAC9B,MAAM,EAAE,KAAK,GAAG,EAAE,EAAE,MAAM,GAAG,QAAQ,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;QAEpD,MAAM,iBAAiB,GAAG,MAAM,KAAK,CAAC,SAAS,EAAE;aAC9C,UAAU,CAAC,YAAY,CAAC;aACxB,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC;aAC7B,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC;aAC7B,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;aAC5B,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;aACpB,GAAG,EAAE,CAAC;QAET,MAAM,SAAS,GAAG,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,iBAClD,EAAE,EAAE,GAAG,CAAC,EAAE,IACP,GAAG,CAAC,IAAI,EAAE,EACb,CAAC,CAAC;QAEJ,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,SAAS;SACV,CAAC,CAAC;KAEJ;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACnD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,KAAK,EAAE,4BAA4B;YACnC,OAAO,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;SAClE,CAAC,CAAC;KACJ;AACH,CAAC,CAAC,CAAC;AAEH,6CAA6C;AAC7C,SAAS,oBAAoB,CAAC,WAAgB,EAAE,QAAgB;IAC9D,MAAM,EAAE,mBAAmB,EAAE,SAAS,EAAE,kBAAkB,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG,WAAW,CAAC;IAErG,OAAO;aACI,QAAQ;;;0BAGK,WAAW;mBAClB,WAAW;0BACJ,mBAAmB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;eACnF,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM;wBAC3C,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAwC5F,CAAC;AACF,CAAC;AAED,sDAAsD;AACtD,SAAS,6BAA6B,CAAC,SAAmB,EAAE,OAAe;IACzE,OAAO;kEACyD,OAAO;;cAE3D,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAgCjC,CAAC;AACF,CAAC;AAED,qCAAqC;AACrC,SAAS,aAAa,CAAC,YAAoB,EAAE,WAAgB,EAAE,QAAgB;IAC7E,IAAI;QACF,6BAA6B;QAC7B,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;KACjC;IAAC,OAAO,KAAK,EAAE;QACd,sDAAsD;QACtD,OAAO,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;QAC/E,OAAO;YACL,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;gBAChD,GAAG,EAAE,CAAC,GAAG,CAAC;gBACV,KAAK,EAAE,EAAE;gBACT,cAAc,EAAE;oBACd,QAAQ,EAAE,WAAW,CAAC,WAAW,IAAI,IAAI;oBACzC,OAAO,EAAE,CAAC;oBACV,KAAK,EAAE,CAAC;oBACR,GAAG,EAAE,CAAC;oBACN,KAAK,EAAE,CAAC;iBACT;aACF,CAAC,CAAC;YACH,WAAW,EAAE,YAAY;SAC1B,CAAC;KACH;AACH,CAAC;AAED,0CAA0C;AAC1C,SAAS,kBAAkB,CAAC,aAAqB;IAC/C,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;KAClC;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,IAAI,CAAC,qEAAqE,CAAC,CAAC;QACpF,OAAO;YACL,cAAc,EAAE;gBACd,QAAQ,EAAE,CAAC;gBACX,OAAO,EAAE,CAAC;gBACV,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,CAAC;gBACN,KAAK,EAAE,CAAC;aACT;YACD,SAAS,EAAE,EAAE;YACb,WAAW,EAAE,CAAC;YACd,eAAe,EAAE,EAAE;YACnB,WAAW,EAAE,aAAa;SAC3B,CAAC;KACH;AACH,CAAC;AAED,gDAAgD;AACnC,QAAA,GAAG,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;AAElD,gDAAgD;AACnC,QAAA,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;IACzE,IAAI;QACF,oCAAoC;QACpC,MAAM,KAAK,CAAC,SAAS,EAAE;aACpB,UAAU,CAAC,OAAO,CAAC;aACnB,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;aACb,GAAG,CAAC;YACH,KAAK,EAAE,IAAI,CAAC,KAAK;YACjB,WAAW,EAAE,IAAI,CAAC,WAAW;YAC7B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;YACvD,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,eAAe,EAAE;YACvD,SAAS,EAAE,KAAK;YAChB,mBAAmB,EAAE,KAAK;SAC3B,CAAC,CAAC;QAEL,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;KACrD;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;KACvD;AACH,CAAC,CAAC,CAAC;AAEH,wCAAwC;AAC3B,QAAA,aAAa,GAAG,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,EAAE,EAAE;IACzE,IAAI;QACF,kCAAkC;QAClC,MAAM,KAAK,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC,KAAK,EAAE,CAAC;QAExC,uBAAuB;QACvB,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAElE,2BAA2B;QAC3B,MAAM,iBAAiB,GAAG,MAAM,KAAK,CAAC,SAAS,EAAE;aAC9C,UAAU,CAAC,YAAY,CAAC;aACxB,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC;aAC/B,GAAG,EAAE,CAAC;QAET,iBAAiB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACnC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;QAEH,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;QACrB,OAAO,CAAC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;KACjD;IAAC,OAAO,KAAK,EAAE;QACd,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;KACnD;AACH,CAAC,CAAC,CAAC"}