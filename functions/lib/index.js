"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.onUserDeleted = exports.onUserCreated = exports.api = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
const openai_1 = require("openai");
const cors_1 = __importDefault(require("cors"));
const express_1 = __importDefault(require("express"));
const Joi = __importStar(require("joi"));
// Initialize Firebase Admin
admin.initializeApp();
// Initialize OpenAI (lazy-loaded)
let openai;
function getOpenAI() {
    var _a;
    if (!openai) {
        const apiKey = process.env.OPENAI_API_KEY || ((_a = functions.config().openai) === null || _a === void 0 ? void 0 : _a.api_key);
        if (!apiKey) {
            throw new Error('OpenAI API key not found in environment variables or Firebase config');
        }
        openai = new openai_1.OpenAI({ apiKey });
    }
    return openai;
}
// Initialize Express app with CORS
const app = (0, express_1.default)();
app.use((0, cors_1.default)({ origin: true }));
app.use(express_1.default.json());
// Validation schemas
const mealPlanRequestSchema = Joi.object({
    userId: Joi.string().optional().default('test-user'),
    preferences: Joi.object({
        dietaryRestrictions: Joi.array().items(Joi.string()).default([]),
        allergies: Joi.array().items(Joi.string()).default([]),
        cuisinePreferences: Joi.array().items(Joi.string()).default([]),
        mealsPerDay: Joi.number().min(1).max(6).default(3),
        calorieGoal: Joi.number().min(1000).max(5000).required(),
        proteinGoal: Joi.number().min(0).default(0),
        carbsGoal: Joi.number().min(0).default(0),
        fatGoal: Joi.number().min(0).default(0),
    }).required(),
    duration: Joi.number().min(1).max(30).default(7), // days
});
const nutritionAnalysisSchema = Joi.object({
    foodItems: Joi.array().items(Joi.string()).min(1).required(),
    portion: Joi.string().default('1 serving'),
});
// Middleware for authentication
const authenticateUser = async (req, res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            res.status(401).json({ error: 'Unauthorized: No valid token provided' });
            return;
        }
        const token = authHeader.split('Bearer ')[1];
        const decodedToken = await admin.auth().verifyIdToken(token);
        req.body.userId = decodedToken.uid;
        next();
    }
    catch (error) {
        console.error('Authentication error:', error);
        res.status(401).json({ error: 'Unauthorized: Invalid token' });
        return;
    }
};
// Generate meal plan using OpenAI (PUBLIC FOR TESTING)
app.post('/generate-meal-plan', async (req, res) => {
    var _a, _b;
    try {
        // Validate request
        const { error, value } = mealPlanRequestSchema.validate(req.body);
        if (error) {
            res.status(400).json({ error: error.details[0].message });
            return;
        }
        const { userId, preferences, duration } = value;
        // Create prompt for OpenAI
        const prompt = createMealPlanPrompt(preferences, duration);
        // Generate meal plan using OpenAI
        const completion = await getOpenAI().chat.completions.create({
            model: 'gpt-4o',
            messages: [
                {
                    role: 'system',
                    content: 'You are a professional nutritionist and meal planning expert. Generate detailed, balanced meal plans with accurate nutritional information.',
                },
                {
                    role: 'user',
                    content: prompt,
                },
            ],
            temperature: 0.7,
            max_tokens: 3000,
        });
        const mealPlanText = (_b = (_a = completion.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content;
        if (!mealPlanText) {
            throw new Error('Failed to generate meal plan');
        }
        // Parse and structure the meal plan
        const mealPlan = parseMealPlan(mealPlanText, preferences, duration);
        // Save to Firestore
        await admin.firestore()
            .collection('meal_plans')
            .add({
            userId,
            mealPlan,
            preferences,
            duration,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            status: 'active',
        });
        res.json({
            success: true,
            mealPlan,
            message: 'Meal plan generated successfully',
        });
    }
    catch (error) {
        console.error('Error generating meal plan:', error);
        res.status(500).json({
            error: 'Failed to generate meal plan',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
// Analyze nutrition using OpenAI
app.post('/analyze-nutrition', authenticateUser, async (req, res) => {
    var _a, _b;
    try {
        // Validate request
        const { error, value } = nutritionAnalysisSchema.validate(req.body);
        if (error) {
            res.status(400).json({ error: error.details[0].message });
            return;
        }
        const { foodItems, portion } = value;
        // Create prompt for nutrition analysis
        const prompt = createNutritionAnalysisPrompt(foodItems, portion);
        // Analyze nutrition using OpenAI
        const completion = await getOpenAI().chat.completions.create({
            model: 'gpt-4',
            messages: [
                {
                    role: 'system',
                    content: 'You are a nutrition expert. Provide accurate nutritional analysis for food items in JSON format.',
                },
                {
                    role: 'user',
                    content: prompt,
                },
            ],
            temperature: 0.3,
            max_tokens: 1000,
        });
        const nutritionText = (_b = (_a = completion.choices[0]) === null || _a === void 0 ? void 0 : _a.message) === null || _b === void 0 ? void 0 : _b.content;
        if (!nutritionText) {
            throw new Error('Failed to analyze nutrition');
        }
        // Parse nutrition data
        const nutritionData = parseNutritionData(nutritionText);
        res.json({
            success: true,
            nutrition: nutritionData,
            message: 'Nutrition analysis completed',
        });
    }
    catch (error) {
        console.error('Error analyzing nutrition:', error);
        res.status(500).json({
            error: 'Failed to analyze nutrition',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
// Get user's meal plans
app.get('/meal-plans/:userId', authenticateUser, async (req, res) => {
    try {
        const { userId } = req.params;
        const { limit = 10, status = 'active' } = req.query;
        const mealPlansSnapshot = await admin.firestore()
            .collection('meal_plans')
            .where('userId', '==', userId)
            .where('status', '==', status)
            .orderBy('createdAt', 'desc')
            .limit(Number(limit))
            .get();
        const mealPlans = mealPlansSnapshot.docs.map(doc => (Object.assign({ id: doc.id }, doc.data())));
        res.json({
            success: true,
            mealPlans,
        });
    }
    catch (error) {
        console.error('Error fetching meal plans:', error);
        res.status(500).json({
            error: 'Failed to fetch meal plans',
            details: error instanceof Error ? error.message : 'Unknown error',
        });
    }
});
// Helper function to create meal plan prompt
function createMealPlanPrompt(preferences, duration) {
    const { dietaryRestrictions, allergies, cuisinePreferences, mealsPerDay, calorieGoal } = preferences;
    return `
Generate a ${duration}-day meal plan with the following requirements:

**Dietary Requirements:**
- Daily calorie target: ${calorieGoal} calories
- Meals per day: ${mealsPerDay}
- Dietary restrictions: ${dietaryRestrictions.length > 0 ? dietaryRestrictions.join(', ') : 'None'}
- Allergies: ${allergies.length > 0 ? allergies.join(', ') : 'None'}
- Preferred cuisines: ${cuisinePreferences.length > 0 ? cuisinePreferences.join(', ') : 'Any'}

**Requirements:**
1. Provide balanced nutrition with appropriate macronutrient distribution
2. Include variety in ingredients and cooking methods
3. Ensure meals are practical and achievable
4. Include estimated preparation time for each meal
5. Provide nutritional breakdown for each meal (calories, protein, carbs, fat, fiber)

**Format the response as JSON with this structure:**
{
  "days": [
    {
      "day": 1,
      "meals": [
        {
          "type": "breakfast",
          "name": "Meal Name",
          "ingredients": ["ingredient1", "ingredient2"],
          "instructions": "Cooking instructions",
          "prepTime": "15 minutes",
          "nutrition": {
            "calories": 400,
            "protein": 20,
            "carbs": 45,
            "fat": 15,
            "fiber": 8
          }
        }
      ],
      "totalNutrition": {
        "calories": 2000,
        "protein": 150,
        "carbs": 200,
        "fat": 65,
        "fiber": 30
      }
    }
  ]
}
`;
}
// Helper function to create nutrition analysis prompt
function createNutritionAnalysisPrompt(foodItems, portion) {
    return `
Analyze the nutritional content of the following food items for ${portion}:

Food items: ${foodItems.join(', ')}

Provide a detailed nutritional breakdown in JSON format:
{
  "totalNutrition": {
    "calories": 0,
    "protein": 0,
    "carbs": 0,
    "fat": 0,
    "fiber": 0,
    "sugar": 0,
    "sodium": 0,
    "cholesterol": 0,
    "vitaminC": 0,
    "calcium": 0,
    "iron": 0
  },
  "breakdown": [
    {
      "item": "food item name",
      "nutrition": {
        "calories": 0,
        "protein": 0,
        "carbs": 0,
        "fat": 0,
        "fiber": 0
      }
    }
  ],
  "healthScore": 85,
  "recommendations": ["recommendation1", "recommendation2"]
}
`;
}
// Helper function to parse meal plan
function parseMealPlan(mealPlanText, preferences, duration) {
    try {
        // Try to parse as JSON first
        return JSON.parse(mealPlanText);
    }
    catch (error) {
        // If JSON parsing fails, create a structured response
        console.warn('Failed to parse meal plan as JSON, creating fallback structure');
        return {
            days: Array.from({ length: duration }, (_, i) => ({
                day: i + 1,
                meals: [],
                totalNutrition: {
                    calories: preferences.calorieGoal || 2000,
                    protein: 0,
                    carbs: 0,
                    fat: 0,
                    fiber: 0,
                },
            })),
            rawResponse: mealPlanText,
        };
    }
}
// Helper function to parse nutrition data
function parseNutritionData(nutritionText) {
    try {
        return JSON.parse(nutritionText);
    }
    catch (error) {
        console.warn('Failed to parse nutrition data as JSON, creating fallback structure');
        return {
            totalNutrition: {
                calories: 0,
                protein: 0,
                carbs: 0,
                fat: 0,
                fiber: 0,
            },
            breakdown: [],
            healthScore: 0,
            recommendations: [],
            rawResponse: nutritionText,
        };
    }
}
// Export the Express app as a Firebase Function
exports.api = functions.https.onRequest(app);
// Trigger function when user profile is created
exports.onUserCreated = functions.auth.user().onCreate(async (user) => {
    try {
        // Create user document in Firestore
        await admin.firestore()
            .collection('users')
            .doc(user.uid)
            .set({
            email: user.email,
            displayName: user.displayName,
            photoURL: user.photoURL,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            updatedAt: admin.firestore.FieldValue.serverTimestamp(),
            isPremium: false,
            onboardingCompleted: false,
        });
        console.log('User document created for:', user.uid);
    }
    catch (error) {
        console.error('Error creating user document:', error);
    }
});
// Trigger function when user is deleted
exports.onUserDeleted = functions.auth.user().onDelete(async (user) => {
    try {
        // Delete user data from Firestore
        const batch = admin.firestore().batch();
        // Delete user document
        batch.delete(admin.firestore().collection('users').doc(user.uid));
        // Delete user's meal plans
        const mealPlansSnapshot = await admin.firestore()
            .collection('meal_plans')
            .where('userId', '==', user.uid)
            .get();
        mealPlansSnapshot.docs.forEach(doc => {
            batch.delete(doc.ref);
        });
        await batch.commit();
        console.log('User data deleted for:', user.uid);
    }
    catch (error) {
        console.error('Error deleting user data:', error);
    }
});
//# sourceMappingURL=index.js.map